{"files.associations": {"cassert": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "map": "cpp", "memory": "cpp", "new": "cpp", "optional": "cpp", "ranges": "cpp", "span": "cpp", "stdexcept": "cpp", "string": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "utility": "cpp", "vector": "cpp", "xhash": "cpp", "xmemory": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}}